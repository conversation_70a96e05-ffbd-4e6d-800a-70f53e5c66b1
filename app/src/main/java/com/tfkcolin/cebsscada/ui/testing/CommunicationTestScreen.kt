package com.tfkcolin.cebsscada.ui.testing

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.communication.models.MqttBroker

/**
 * Comprehensive test screen for all communication protocols
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommunicationTestScreen(
    viewModel: CommunicationTestViewModel = hiltViewModel()
) {
    val testResults by viewModel.testResults.collectAsStateWithLifecycle()
    val isRunningTests by viewModel.isRunningTests.collectAsStateWithLifecycle()
    val supportedProtocols by viewModel.supportedProtocols.collectAsStateWithLifecycle()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Communication Protocol Test Suite",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "Test all supported communication protocols to ensure proper functionality",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = { viewModel.runAllTests() },
                        enabled = !isRunningTests,
                        modifier = Modifier.weight(1f)
                    ) {
                        if (isRunningTests) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                strokeWidth = 2.dp
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                        }
                        Text("Run All Tests")
                    }
                    
                    OutlinedButton(
                        onClick = { viewModel.clearResults() },
                        enabled = !isRunningTests
                    ) {
                        Text("Clear Results")
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Test Configuration
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Test Configuration",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // MQTT Test Configuration
                if (supportedProtocols.contains(CommunicationProtocol.MQTT)) {
                    MqttTestConfiguration(
                        onConfigurationChanged = viewModel::updateMqttTestConfig
                    )
                }
                
                // Bluetooth Test Configuration
                if (supportedProtocols.contains(CommunicationProtocol.BLUETOOTH_CLASSIC) ||
                    supportedProtocols.contains(CommunicationProtocol.BLUETOOTH_BLE)) {
                    BluetoothTestConfiguration()
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Test Results
        Text(
            text = "Test Results",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        if (testResults.isEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "No test results yet. Run tests to see results.",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        } else {
            LazyColumn(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(testResults) { result ->
                    TestResultItem(result = result)
                }
            }
        }
    }
}

@Composable
fun MqttTestConfiguration(
    onConfigurationChanged: (MqttBroker) -> Unit
) {
    var brokerUrl by remember { mutableStateOf("test.mosquitto.org") }
    var port by remember { mutableStateOf("1883") }
    var username by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var useTls by remember { mutableStateOf(false) }
    
    LaunchedEffect(brokerUrl, port, username, password, useTls) {
        val broker = MqttBroker(
            id = "test_broker",
            name = "Test MQTT Broker",
            address = brokerUrl,
            port = port.toIntOrNull() ?: 1883,
            username = username.ifBlank { null },
            password = password.ifBlank { null },
            useTls = useTls
        )
        onConfigurationChanged(broker)
    }
    
    Column {
        Text(
            text = "MQTT Configuration",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        OutlinedTextField(
            value = brokerUrl,
            onValueChange = { brokerUrl = it },
            label = { Text("Broker URL") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        OutlinedTextField(
            value = port,
            onValueChange = { port = it },
            label = { Text("Port") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = username,
                onValueChange = { username = it },
                label = { Text("Username (optional)") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )
            
            OutlinedTextField(
                value = password,
                onValueChange = { password = it },
                label = { Text("Password (optional)") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = useTls,
                onCheckedChange = { useTls = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("Use TLS/SSL")
        }
    }
}

@Composable
fun BluetoothTestConfiguration() {
    Column {
        Text(
            text = "Bluetooth Configuration",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "Bluetooth tests will use device discovery and mock connections.",
            style = MaterialTheme.typography.bodySmall
        )
    }
}

@Composable
fun TestResultItem(result: TestResult) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
        colors = CardDefaults.cardColors(
            containerColor = when (result.status) {
                TestStatus.PASSED -> MaterialTheme.colorScheme.primaryContainer
                TestStatus.FAILED -> MaterialTheme.colorScheme.errorContainer
                TestStatus.RUNNING -> MaterialTheme.colorScheme.secondaryContainer
                TestStatus.SKIPPED -> MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = result.testName,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )
                
                Text(
                    text = result.protocol.name.replace("_", " "),
                    style = MaterialTheme.typography.bodySmall
                )
                
                if (result.message.isNotBlank()) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = result.message,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                
                Text(
                    text = "Duration: ${result.duration}ms",
                    style = MaterialTheme.typography.labelSmall
                )
            }
            
            Icon(
                imageVector = when (result.status) {
                    TestStatus.PASSED -> Icons.Default.CheckCircle
                    TestStatus.FAILED -> Icons.Default.Error
                    TestStatus.RUNNING -> Icons.Default.Refresh
                    TestStatus.SKIPPED -> Icons.Default.SkipNext
                },
                contentDescription = result.status.name,
                tint = when (result.status) {
                    TestStatus.PASSED -> MaterialTheme.colorScheme.primary
                    TestStatus.FAILED -> MaterialTheme.colorScheme.error
                    TestStatus.RUNNING -> MaterialTheme.colorScheme.secondary
                    TestStatus.SKIPPED -> MaterialTheme.colorScheme.onSurfaceVariant
                }
            )
        }
    }
}

data class TestResult(
    val testName: String,
    val protocol: CommunicationProtocol,
    val status: TestStatus,
    val message: String = "",
    val duration: Long = 0,
    val timestamp: Long = System.currentTimeMillis()
)

enum class TestStatus {
    RUNNING,
    PASSED,
    FAILED,
    SKIPPED
}
