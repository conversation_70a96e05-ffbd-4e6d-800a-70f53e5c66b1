package com.tfkcolin.cebsscada.ui.bluetooth

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.cebsscada.bluetooth.BluetoothState
import com.tfkcolin.cebsscada.bluetooth.BluetoothViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BluetoothScreen(viewModel: BluetoothViewModel = hiltViewModel()) {
    val bluetoothState by viewModel.bluetoothState
    val discoveredDevices = viewModel.discoveredDevices
    val messages = viewModel.messages
    val isScanning by viewModel.isScanning
    val isBluetoothEnabled by viewModel.isBluetoothEnabled
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Bluetooth status bar
        BluetoothStatusBar(
            isEnabled = isBluetoothEnabled,
            state = bluetoothState,
            onEnable = { viewModel.enableBluetooth() }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Device list and controls
        when (val currentState = bluetoothState) {
            is BluetoothState.Connected -> {
                ConnectedDeviceScreen(
                    device = currentState.device,
                    messages = messages,
                    onSendMessage = { viewModel.sendMessage(it) },
                    onDisconnect = { viewModel.disconnect() },
                    onClearMessages = { viewModel.clearMessages() }
                )
            }
            else -> {
                DeviceListScreen(
                    devices = discoveredDevices,
                    isScanning = isScanning,
                    onScanStart = { viewModel.startScan() },
                    onScanStop = { viewModel.stopScan() },
                    onDeviceClick = { viewModel.connect(it) }
                )
            }
        }
    }
}
