package com.tfkcolin.cebsscada.ui.testing

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.cebsscada.communication.CommunicationManager
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.communication.QoSLevel
import com.tfkcolin.cebsscada.communication.models.MqttBroker
import com.tfkcolin.cebsscada.communication.models.TopicSubscription
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import javax.inject.Inject

/**
 * ViewModel for communication protocol testing
 */
@HiltViewModel
class CommunicationTestViewModel @Inject constructor(
    private val communicationManager: CommunicationManager
) : ViewModel() {
    
    private val _testResults = MutableStateFlow<List<TestResult>>(emptyList())
    val testResults: StateFlow<List<TestResult>> = _testResults.asStateFlow()
    
    private val _isRunningTests = MutableStateFlow(false)
    val isRunningTests: StateFlow<Boolean> = _isRunningTests.asStateFlow()
    
    private val _supportedProtocols = MutableStateFlow(communicationManager.getSupportedProtocols())
    val supportedProtocols: StateFlow<List<CommunicationProtocol>> = _supportedProtocols.asStateFlow()
    
    private var mqttTestConfig = MqttBroker(
        id = "test_broker",
        name = "Test MQTT Broker",
        address = "test.mosquitto.org",
        port = 1883
    )
    
    /**
     * Update MQTT test configuration
     */
    fun updateMqttTestConfig(broker: MqttBroker) {
        mqttTestConfig = broker
    }
    
    /**
     * Run all available tests
     */
    fun runAllTests() {
        if (_isRunningTests.value) return
        
        viewModelScope.launch {
            _isRunningTests.value = true
            _testResults.value = emptyList()
            
            try {
                val protocols = _supportedProtocols.value
                
                for (protocol in protocols) {
                    when (protocol) {
                        CommunicationProtocol.BLUETOOTH_CLASSIC,
                        CommunicationProtocol.BLUETOOTH_BLE -> {
                            runBluetoothTests(protocol)
                        }
                        CommunicationProtocol.MQTT -> {
                            runMqttTests()
                        }
                        else -> {
                            addTestResult(
                                TestResult(
                                    testName = "Protocol Support Test",
                                    protocol = protocol,
                                    status = TestStatus.SKIPPED,
                                    message = "Tests not implemented for this protocol yet"
                                )
                            )
                        }
                    }
                }
            } finally {
                _isRunningTests.value = false
            }
        }
    }
    
    /**
     * Clear test results
     */
    fun clearResults() {
        _testResults.value = emptyList()
    }
    
    private suspend fun runBluetoothTests(protocol: CommunicationProtocol) {
        // Test 1: Service Creation
        addTestResult(
            TestResult(
                testName = "Service Creation Test",
                protocol = protocol,
                status = TestStatus.RUNNING
            )
        )
        
        val startTime = System.currentTimeMillis()
        
        try {
            val service = communicationManager.getService(protocol)
            val duration = System.currentTimeMillis() - startTime
            
            if (service != null) {
                updateLastTestResult(
                    TestStatus.PASSED,
                    "Service created successfully",
                    duration
                )
            } else {
                updateLastTestResult(
                    TestStatus.FAILED,
                    "Failed to create service",
                    duration
                )
                return
            }
        } catch (e: Exception) {
            updateLastTestResult(
                TestStatus.FAILED,
                "Exception: ${e.message}",
                System.currentTimeMillis() - startTime
            )
            return
        }
        
        // Test 2: Device Discovery
        addTestResult(
            TestResult(
                testName = "Device Discovery Test",
                protocol = protocol,
                status = TestStatus.RUNNING
            )
        )
        
        val discoveryStartTime = System.currentTimeMillis()
        
        try {
            communicationManager.startDiscovery(protocol)
            delay(5000) // Wait 5 seconds for discovery
            communicationManager.stopDiscovery(protocol)
            
            val devices = communicationManager.getDevices(protocol).first()
            val discoveryDuration = System.currentTimeMillis() - discoveryStartTime
            
            updateLastTestResult(
                TestStatus.PASSED,
                "Discovery completed. Found ${devices.size} devices",
                discoveryDuration
            )
        } catch (e: Exception) {
            updateLastTestResult(
                TestStatus.FAILED,
                "Discovery failed: ${e.message}",
                System.currentTimeMillis() - discoveryStartTime
            )
        }
        
        // Test 3: Connection State Management
        addTestResult(
            TestResult(
                testName = "Connection State Test",
                protocol = protocol,
                status = TestStatus.RUNNING
            )
        )
        
        val stateTestStartTime = System.currentTimeMillis()
        
        try {
            val connectionStates = communicationManager.connectionStates.first()
            val currentState = connectionStates[protocol] ?: ConnectionState.DISCONNECTED
            
            updateLastTestResult(
                TestStatus.PASSED,
                "Connection state: $currentState",
                System.currentTimeMillis() - stateTestStartTime
            )
        } catch (e: Exception) {
            updateLastTestResult(
                TestStatus.FAILED,
                "State test failed: ${e.message}",
                System.currentTimeMillis() - stateTestStartTime
            )
        }
    }
    
    private suspend fun runMqttTests() {
        // Test 1: Service Creation
        addTestResult(
            TestResult(
                testName = "MQTT Service Creation Test",
                protocol = CommunicationProtocol.MQTT,
                status = TestStatus.RUNNING
            )
        )
        
        val startTime = System.currentTimeMillis()
        
        try {
            val service = communicationManager.getService(CommunicationProtocol.MQTT)
            val duration = System.currentTimeMillis() - startTime
            
            if (service != null) {
                updateLastTestResult(
                    TestStatus.PASSED,
                    "MQTT service created successfully",
                    duration
                )
            } else {
                updateLastTestResult(
                    TestStatus.FAILED,
                    "Failed to create MQTT service",
                    duration
                )
                return
            }
        } catch (e: Exception) {
            updateLastTestResult(
                TestStatus.FAILED,
                "Exception: ${e.message}",
                System.currentTimeMillis() - startTime
            )
            return
        }
        
        // Test 2: Connection Test
        addTestResult(
            TestResult(
                testName = "MQTT Connection Test",
                protocol = CommunicationProtocol.MQTT,
                status = TestStatus.RUNNING
            )
        )
        
        val connectionStartTime = System.currentTimeMillis()
        
        try {
            val connected = communicationManager.connect(mqttTestConfig)
            
            if (connected) {
                // Wait for connection to establish
                val connectionResult = withTimeoutOrNull(10000) {
                    var state = ConnectionState.CONNECTING
                    while (state != ConnectionState.CONNECTED && state != ConnectionState.ERROR) {
                        delay(100)
                        state = communicationManager.connectionStates.first()[CommunicationProtocol.MQTT] 
                            ?: ConnectionState.DISCONNECTED
                    }
                    state == ConnectionState.CONNECTED
                }
                
                val duration = System.currentTimeMillis() - connectionStartTime
                
                if (connectionResult == true) {
                    updateLastTestResult(
                        TestStatus.PASSED,
                        "Connected to MQTT broker successfully",
                        duration
                    )
                    
                    // Run additional tests if connected
                    runMqttSubscriptionTest()
                    runMqttPublishTest()
                    
                    // Disconnect
                    communicationManager.disconnect(CommunicationProtocol.MQTT)
                } else {
                    updateLastTestResult(
                        TestStatus.FAILED,
                        "Connection timeout or failed",
                        duration
                    )
                }
            } else {
                updateLastTestResult(
                    TestStatus.FAILED,
                    "Failed to initiate connection",
                    System.currentTimeMillis() - connectionStartTime
                )
            }
        } catch (e: Exception) {
            updateLastTestResult(
                TestStatus.FAILED,
                "Connection test failed: ${e.message}",
                System.currentTimeMillis() - connectionStartTime
            )
        }
    }
    
    private suspend fun runMqttSubscriptionTest() {
        addTestResult(
            TestResult(
                testName = "MQTT Subscription Test",
                protocol = CommunicationProtocol.MQTT,
                status = TestStatus.RUNNING
            )
        )
        
        val startTime = System.currentTimeMillis()
        
        try {
            val testTopic = "test/android/subscription"
            val subscription = TopicSubscription(testTopic, QoSLevel.AT_LEAST_ONCE)
            
            val subscribed = communicationManager.subscribe(CommunicationProtocol.MQTT, subscription)
            val duration = System.currentTimeMillis() - startTime
            
            if (subscribed) {
                updateLastTestResult(
                    TestStatus.PASSED,
                    "Successfully subscribed to topic: $testTopic",
                    duration
                )
                
                // Unsubscribe
                communicationManager.unsubscribe(CommunicationProtocol.MQTT, testTopic)
            } else {
                updateLastTestResult(
                    TestStatus.FAILED,
                    "Failed to subscribe to topic",
                    duration
                )
            }
        } catch (e: Exception) {
            updateLastTestResult(
                TestStatus.FAILED,
                "Subscription test failed: ${e.message}",
                System.currentTimeMillis() - startTime
            )
        }
    }
    
    private suspend fun runMqttPublishTest() {
        addTestResult(
            TestResult(
                testName = "MQTT Publish Test",
                protocol = CommunicationProtocol.MQTT,
                status = TestStatus.RUNNING
            )
        )
        
        val startTime = System.currentTimeMillis()
        
        try {
            val testTopic = "test/android/publish"
            val testMessage = "Hello from Android SCADA Test - ${System.currentTimeMillis()}"
            
            val result = communicationManager.sendText(CommunicationProtocol.MQTT, testMessage, testTopic)
            val duration = System.currentTimeMillis() - startTime
            
            if (result.success) {
                updateLastTestResult(
                    TestStatus.PASSED,
                    "Successfully published message to topic: $testTopic",
                    duration
                )
            } else {
                updateLastTestResult(
                    TestStatus.FAILED,
                    "Failed to publish message: ${result.error}",
                    duration
                )
            }
        } catch (e: Exception) {
            updateLastTestResult(
                TestStatus.FAILED,
                "Publish test failed: ${e.message}",
                System.currentTimeMillis() - startTime
            )
        }
    }
    
    private fun addTestResult(result: TestResult) {
        val currentResults = _testResults.value.toMutableList()
        currentResults.add(result)
        _testResults.value = currentResults
    }
    
    private fun updateLastTestResult(status: TestStatus, message: String, duration: Long) {
        val currentResults = _testResults.value.toMutableList()
        if (currentResults.isNotEmpty()) {
            val lastResult = currentResults.last()
            currentResults[currentResults.size - 1] = lastResult.copy(
                status = status,
                message = message,
                duration = duration
            )
            _testResults.value = currentResults
        }
    }
}
