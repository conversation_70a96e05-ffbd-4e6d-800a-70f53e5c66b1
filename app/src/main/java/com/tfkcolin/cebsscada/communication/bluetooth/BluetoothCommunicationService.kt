package com.tfkcolin.cebsscada.communication.bluetooth

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice as AndroidBluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCallback
import android.bluetooth.BluetoothSocket
import android.content.Context
import android.util.Log
import androidx.annotation.RequiresPermission
import com.tfkcolin.cebsscada.bluetooth.BluetoothConstants
import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.communication.base.BaseCommunicationService
import com.tfkcolin.cebsscada.communication.models.BluetoothDevice
import com.tfkcolin.cebsscada.communication.models.CommunicationDevice
import com.tfkcolin.cebsscada.communication.models.CommunicationMessage
import com.tfkcolin.cebsscada.communication.models.MessageDirection
import com.tfkcolin.cebsscada.communication.models.MessageResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.IOException
import java.util.UUID
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject

/**
 * Bluetooth communication service implementing the new architecture
 */
class BluetoothCommunicationService @Inject constructor(
    private val context: Context,
    private val bluetoothAdapter: BluetoothAdapter?
) : BaseCommunicationService() {
    
    companion object {
        private const val TAG = "BluetoothCommService"
        private val UUID_SPP = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB")
    }
    
    private var socket: BluetoothSocket? = null
    private var bleGatt: BluetoothGatt? = null
    private var connectedThread: ConnectedThread? = null
    private val isConnecting = AtomicBoolean(false)
    
    override suspend fun connect(device: CommunicationDevice, options: Map<String, Any>): Boolean {
        if (device !is BluetoothDevice) {
            emitConnectionError("Invalid device type for Bluetooth service")
            return false
        }
        
        if (bluetoothAdapter == null) {
            emitConnectionError("Bluetooth adapter not available")
            return false
        }
        
        if (isConnected || isConnecting.get()) {
            disconnect()
        }
        
        isConnecting.set(true)
        updateConnectionState(ConnectionState.CONNECTING)
        updateConnectedDevice(device)
        
        return try {
            when (device.deviceType) {
                com.tfkcolin.cebsscada.communication.DeviceType.BLUETOOTH_CLASSIC -> {
                    connectClassic(device)
                }
                com.tfkcolin.cebsscada.communication.DeviceType.BLUETOOTH_BLE -> {
                    connectBLE(device)
                }
                else -> {
                    emitConnectionError("Unsupported Bluetooth device type")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Connection failed", e)
            updateConnectionState(ConnectionState.ERROR)
            emitConnectionError("Connection failed: ${e.message}")
            false
        } finally {
            isConnecting.set(false)
        }
    }
    
    @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
    private suspend fun connectClassic(device: BluetoothDevice): Boolean = withContext(Dispatchers.IO) {
        try {
            val androidDevice = bluetoothAdapter?.getRemoteDevice(device.address)
            val tmpSocket = androidDevice?.createRfcommSocketToServiceRecord(UUID_SPP)
            
            bluetoothAdapter?.cancelDiscovery()
            tmpSocket?.connect()
            
            socket = tmpSocket
            connectedThread = ConnectedThread(socket)
            connectedThread?.start()
            
            updateConnectionState(ConnectionState.CONNECTED)
            true
        } catch (e: IOException) {
            Log.e(TAG, "Classic Bluetooth connection failed", e)
            updateConnectionState(ConnectionState.ERROR)
            emitConnectionError("Classic Bluetooth connection failed: ${e.message}")
            closeSocket()
            false
        }
    }
    
    @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
    private suspend fun connectBLE(device: BluetoothDevice): Boolean = withContext(Dispatchers.Main) {
        try {
            val androidDevice = bluetoothAdapter?.getRemoteDevice(device.address)
            val callback = object : BluetoothGattCallback() {
                override fun onConnectionStateChange(gatt: BluetoothGatt?, status: Int, newState: Int) {
                    when (newState) {
                        android.bluetooth.BluetoothProfile.STATE_CONNECTED -> {
                            updateConnectionState(ConnectionState.CONNECTED)
                            gatt?.discoverServices()
                        }
                        android.bluetooth.BluetoothProfile.STATE_DISCONNECTED -> {
                            updateConnectionState(ConnectionState.DISCONNECTED)
                            closeBLEConnection()
                        }
                    }
                }
                
                override fun onCharacteristicChanged(gatt: BluetoothGatt?, characteristic: android.bluetooth.BluetoothGattCharacteristic?) {
                    characteristic?.value?.let { data ->
                        val message = CommunicationMessage.createBinaryMessage(
                            deviceId = device.id,
                            protocol = device.protocol,
                            data = data,
                            direction = MessageDirection.INCOMING
                        )
                        kotlinx.coroutines.runBlocking {
                            emitIncomingMessage(message)
                        }
                    }
                }
            }
            
            bleGatt = androidDevice?.connectGatt(context, false, callback)
            true
        } catch (e: Exception) {
            Log.e(TAG, "BLE connection failed", e)
            updateConnectionState(ConnectionState.ERROR)
            emitConnectionError("BLE connection failed: ${e.message}")
            false
        }
    }
    
    override suspend fun disconnect() {
        updateConnectionState(ConnectionState.DISCONNECTING)
        
        connectedThread?.cancel()
        connectedThread = null
        
        closeSocket()
        closeBLEConnection()
        
        updateConnectionState(ConnectionState.DISCONNECTED)
        updateConnectedDevice(null)
    }
    
    override suspend fun sendMessage(message: CommunicationMessage): MessageResult {
        if (!isConnected) {
            return MessageResult(
                success = false,
                messageId = message.id,
                error = "Not connected to device"
            )
        }
        
        return try {
            when (connectedDevice?.deviceType) {
                com.tfkcolin.cebsscada.communication.DeviceType.BLUETOOTH_CLASSIC -> {
                    sendClassicMessage(message)
                }
                com.tfkcolin.cebsscada.communication.DeviceType.BLUETOOTH_BLE -> {
                    sendBLEMessage(message)
                }
                else -> {
                    MessageResult(
                        success = false,
                        messageId = message.id,
                        error = "Unsupported device type"
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send message", e)
            MessageResult(
                success = false,
                messageId = message.id,
                error = "Send failed: ${e.message}"
            )
        }
    }
    
    private suspend fun sendClassicMessage(message: CommunicationMessage): MessageResult {
        return try {
            socket?.outputStream?.write(message.content)
            trackSentMessage(message)
            MessageResult(success = true, messageId = message.id)
        } catch (e: IOException) {
            MessageResult(
                success = false,
                messageId = message.id,
                error = "Write failed: ${e.message}"
            )
        }
    }
    
    private suspend fun sendBLEMessage(message: CommunicationMessage): MessageResult {
        // BLE message sending would require characteristic writing
        // This is a simplified implementation
        return MessageResult(
            success = false,
            messageId = message.id,
            error = "BLE message sending not fully implemented"
        )
    }
    
    override suspend fun startDiscovery() {
        updateDiscoveryState(true)
        bluetoothAdapter?.startDiscovery()
    }
    
    override suspend fun stopDiscovery() {
        bluetoothAdapter?.cancelDiscovery()
        updateDiscoveryState(false)
    }
    
    override suspend fun cleanup() {
        disconnect()
        connectedThread?.cancel()
    }
    
    private fun closeSocket() {
        try {
            socket?.close()
        } catch (e: IOException) {
            Log.e(TAG, "Error closing socket", e)
        } finally {
            socket = null
        }
    }
    
    private fun closeBLEConnection() {
        bleGatt?.close()
        bleGatt = null
    }
    
    /**
     * Thread for handling Classic Bluetooth communication
     */
    private inner class ConnectedThread(private val socket: BluetoothSocket?) {
        private val inputStream = socket?.inputStream
        private val outputStream = socket?.outputStream
        private val buffer = ByteArray(1024)
        private val shouldContinue = AtomicBoolean(true)
        
        private val readThread = Thread {
            while (shouldContinue.get()) {
                try {
                    val bytes = inputStream?.read(buffer) ?: break
                    val data = buffer.copyOf(bytes)
                    
                    val device = connectedDevice as? BluetoothDevice ?: return@Thread
                    val message = CommunicationMessage.createBinaryMessage(
                        deviceId = device.id,
                        protocol = device.protocol,
                        data = data,
                        direction = MessageDirection.INCOMING
                    )
                    
                    kotlinx.coroutines.runBlocking {
                        emitIncomingMessage(message)
                    }
                } catch (e: IOException) {
                    if (shouldContinue.get()) {
                        updateConnectionState(ConnectionState.ERROR)
                        kotlinx.coroutines.runBlocking {
                            emitConnectionError("Connection lost: ${e.message}")
                        }
                    }
                    break
                }
            }
        }
        
        fun start() {
            readThread.start()
        }
        
        fun cancel() {
            shouldContinue.set(false)
            readThread.interrupt()
        }
    }
}
