package com.tfkcolin.cebsscada.di

import android.bluetooth.BluetoothAdapter
import android.content.Context
import com.tfkcolin.cebsscada.bluetooth.BLEScanner
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object BluetoothModule {

    @Provides
    @Singleton
    fun provideBLEScanner(@ApplicationContext context: Context): BLEScanner {
        return BLEScanner(context)
    }

    @Provides
    @Singleton
    fun provideBluetoothAdapter(): BluetoothAdapter? {
        return BluetoothAdapter.getDefaultAdapter()
    }
}
