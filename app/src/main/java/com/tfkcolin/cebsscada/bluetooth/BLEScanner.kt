package com.tfkcolin.cebsscada.bluetooth

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.le.BluetoothLeScanner
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanFilter
import android.bluetooth.le.ScanResult
import android.bluetooth.le.ScanSettings
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.ParcelUuid
import androidx.annotation.RequiresPermission
import java.util.*

class BLEScanner(private val context: Context) {
    private var bluetoothAdapter: BluetoothAdapter? = null
    private var bluetoothLeScanner: BluetoothLeScanner? = null
    private var scanning = false
    private var scanCallback: ScanCallback? = null

    init {
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        if (bluetoothAdapter != null) {
            bluetoothLeScanner = bluetoothAdapter!!.bluetoothLeScanner
        }
    }

    @RequiresPermission(Manifest.permission.BLUETOOTH_SCAN)
    fun startScan(serviceUUID: UUID? = null) {
        if (scanning) return
        
        if (bluetoothLeScanner == null) {
            sendError(BluetoothConstants.ERROR_BLE_NOT_SUPPORTED)
            return
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            scanCallback = object : ScanCallback() {
                override fun onScanResult(callbackType: Int, result: ScanResult) {
                    super.onScanResult(callbackType, result)
                    val intent = Intent(BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED).apply {
                        putExtra("DEVICE", result.device)
                        putExtra("RSSI", result.rssi)
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            result.scanRecord?.let { scanRecord ->
                                putExtra("DEVICE_NAME", scanRecord.deviceName)
                            }
                        }
                    }
                    context.sendBroadcast(intent)
                }

                override fun onBatchScanResults(results: MutableList<ScanResult>) {
                    super.onBatchScanResults(results)
                    results.forEach { result ->
                        val intent = Intent(BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED).apply {
                            putExtra("DEVICE", result.device)
                            putExtra("RSSI", result.rssi)
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                result.scanRecord?.let { scanRecord ->
                                    putExtra("DEVICE_NAME", scanRecord.deviceName)
                                }
                            }
                        }
                        context.sendBroadcast(intent)
                    }
                }

                override fun onScanFailed(errorCode: Int) {
                    super.onScanFailed(errorCode)
                    sendError(BluetoothConstants.ERROR_BLE_NOT_SUPPORTED)
                }
            }

            val scanFilters = mutableListOf<ScanFilter>()
            serviceUUID?.let {
                scanFilters.add(ScanFilter.Builder().setServiceUuid(ParcelUuid(it)).build())
            }

            val scanSettings = ScanSettings.Builder()
                .setScanMode(ScanSettings.SCAN_MODE_LOW_POWER)
                .build()

            bluetoothLeScanner?.startScan(scanFilters, scanSettings, scanCallback)
            scanning = true
        }
    }

    @RequiresPermission(Manifest.permission.BLUETOOTH_SCAN)
    fun stopScan() {
        if (!scanning) return

        bluetoothLeScanner?.stopScan(scanCallback)
        scanCallback = null
        scanning = false
        
        val intent = Intent(BluetoothConstants.ACTION_DISCOVERY_FINISHED)
        context.sendBroadcast(intent)
    }

    fun isScanning(): Boolean = scanning

    private fun sendError(errorCode: Int) {
        val intent = Intent(BluetoothConstants.ACTION_CONNECTION_FAILED).apply {
            putExtra("ERROR_CODE", errorCode)
            putExtra("ERROR_MESSAGE", BluetoothConstants.ERROR_MESSAGES[errorCode] ?: "Unknown error")
        }
        context.sendBroadcast(intent)
    }
}
