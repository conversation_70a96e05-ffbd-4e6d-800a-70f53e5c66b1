package com.tfkcolin.cebsscada.bluetooth

import android.Manifest
import android.app.Application
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.ServiceConnection
import android.os.Build
import android.os.IBinder
import androidx.annotation.RequiresPermission
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.core.content.ContextCompat
import androidx.lifecycle.AndroidViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject

@HiltViewModel
class BluetoothViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val bleScanner: BLEScanner
) : AndroidViewModel(context.applicationContext as Application) {
    private var bluetoothService: BluetoothService? = null
    
    private val _bluetoothState = mutableStateOf<BluetoothState>(BluetoothState.Idle)
    val bluetoothState: State<BluetoothState> = _bluetoothState
    
    private val _discoveredDevices = mutableStateListOf<BluetoothDeviceWrapper>()
    val discoveredDevices: List<BluetoothDeviceWrapper> = _discoveredDevices
    
    private val _messages = mutableStateListOf<String>()
    val messages: List<String> = _messages
    
    private val _isScanning = mutableStateOf(false)
    val isScanning: State<Boolean> = _isScanning
    
    private val _isBluetoothEnabled = mutableStateOf(false)
    val isBluetoothEnabled: State<Boolean> = _isBluetoothEnabled
    
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(className: ComponentName, service: IBinder) {
            val binder = service as BluetoothService.LocalBinder
            bluetoothService = binder.getService()
            bluetoothService?.initialize()
        }
        
        override fun onServiceDisconnected(arg0: ComponentName) {
            bluetoothService = null
        }
    }
    
    private val bluetoothReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                BluetoothConstants.ACTION_CONNECTED -> {
                    val deviceAddress = intent.getStringExtra("DEVICE_ADDRESS")
                    val device = _discoveredDevices.find { it.address == deviceAddress }?.device
                    device?.let {
                        _bluetoothState.value = BluetoothState.Connected(it)
                    }
                }
                BluetoothConstants.ACTION_DISCONNECTED -> {
                    _bluetoothState.value = BluetoothState.Idle
                }
                BluetoothConstants.ACTION_CONNECTION_FAILED -> {
                    val errorCode = intent.getIntExtra("ERROR_CODE", BluetoothConstants.ERROR_NONE)
                    val errorMessage = intent.getStringExtra("ERROR_MESSAGE") ?: "Unknown error"
                    _bluetoothState.value = BluetoothState.Error(errorMessage, errorCode)
                }
                BluetoothConstants.ACTION_MESSAGE_RECEIVED -> {
                    val message = intent.getStringExtra("MESSAGE") ?: ""
                    _messages.add("Received: $message")
                }
                BluetoothConstants.ACTION_DEVICE_DISCOVERED, 
                BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED -> {
                    val device: BluetoothDevice? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intent.getParcelableExtra("DEVICE", BluetoothDevice::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        intent.getParcelableExtra("DEVICE")
                    }
                    val rssi = intent.getIntExtra("RSSI", 0)
                    val isBLE = intent.action == BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED
                    device?.let {
                        val wrapper = BluetoothDeviceWrapper(
                            device = it,
                            name = it.name ?: "Unknown",
                            address = it.address,
                            type = if (isBLE) DeviceType.BLE else DeviceType.CLASSIC,
                            rssi = if (isBLE) rssi else null,
                            isBonded = it.bondState == BluetoothDevice.BOND_BONDED
                        )
                        if (!_discoveredDevices.any { it.address == wrapper.address }) {
                            _discoveredDevices.add(wrapper)
                        }
                    }
                }
                BluetoothConstants.ACTION_DISCOVERY_FINISHED -> {
                    _isScanning.value = false
                }
                BluetoothConstants.ACTION_BLUETOOTH_ENABLED -> {
                    _isBluetoothEnabled.value = true
                }
                BluetoothConstants.ACTION_BLUETOOTH_DISABLED -> {
                    _isBluetoothEnabled.value = false
                }
            }
        }
    }
    
    init {
        // Check initial Bluetooth state
        val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        _isBluetoothEnabled.value = bluetoothAdapter?.isEnabled == true
        
        // Register receiver
        val filter = IntentFilter().apply {
            addAction(BluetoothConstants.ACTION_CONNECTED)
            addAction(BluetoothConstants.ACTION_DISCONNECTED)
            addAction(BluetoothConstants.ACTION_CONNECTION_FAILED)
            addAction(BluetoothConstants.ACTION_MESSAGE_RECEIVED)
            addAction(BluetoothConstants.ACTION_DEVICE_DISCOVERED)
            addAction(BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED)
            addAction(BluetoothConstants.ACTION_DISCOVERY_FINISHED)
            addAction(BluetoothConstants.ACTION_BLUETOOTH_ENABLED)
            addAction(BluetoothConstants.ACTION_BLUETOOTH_DISABLED)
        }
        ContextCompat.registerReceiver(
            context,
            bluetoothReceiver,
            filter,
            ContextCompat.RECEIVER_NOT_EXPORTED
        )
        
        // Bind to service
        Intent(context, BluetoothService::class.java).also { intent ->
            context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
        }
    }
    
    @RequiresPermission(Manifest.permission.BLUETOOTH_SCAN)
    override fun onCleared() {
        super.onCleared()
        context.unregisterReceiver(bluetoothReceiver)
        context.unbindService(serviceConnection)
        bleScanner.stopScan()
    }
    
    @RequiresPermission(Manifest.permission.BLUETOOTH_SCAN)
    fun startScan() {
        _discoveredDevices.clear()
        _isScanning.value = true
        bleScanner.startScan()
        
        // Also start classic Bluetooth discovery
        val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        bluetoothAdapter?.startDiscovery()
    }
    
    @RequiresPermission(Manifest.permission.BLUETOOTH_SCAN)
    fun stopScan() {
        bleScanner.stopScan()
        val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        bluetoothAdapter?.cancelDiscovery()
        _isScanning.value = false
    }
    
    fun connect(device: BluetoothDeviceWrapper) {
        _bluetoothState.value = BluetoothState.Connecting
        bluetoothService?.connect(device.device, device.type == DeviceType.BLE)
    }
    
    fun disconnect() {
        _bluetoothState.value = BluetoothState.Disconnecting
        bluetoothService?.disconnect()
    }
    
    fun sendMessage(message: String) {
        bluetoothService?.write(message.toByteArray())
        _messages.add("Sent: $message")
    }
    
    fun clearMessages() {
        _messages.clear()
    }
    
    @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
    fun enableBluetooth() {
        val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
        enableBtIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(enableBtIntent)
    }
}
